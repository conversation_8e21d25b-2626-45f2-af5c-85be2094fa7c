<?php

use App\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\BeritaController;
use App\Http\Controllers\PrestasiController;
use App\Http\Controllers\SettingController;

Route::get('/', [BeritaController::class, 'index'])->name('home');

Route::get('/landing-page', [BeritaController::class, 'index']);

Route::get('/landingPage', [BeritaController::class, 'index'])->name('landing');

// API endpoint untuk polling berita
Route::get('/api/berita', [BeritaController::class, 'getBeritaApi']);

// API endpoint untuk prestasi landing page
Route::get('/api/prestasi', [PrestasiController::class, 'getPrestasiForLanding']);

// Route untuk detail berita (public)
Route::get('/berita/{id}', [BeritaController::class, 'detail'])->name('berita.detail');

// Route untuk halaman kegiatan (public)
Route::get('/kegiatan', [AdminController::class, 'publicKegiatan'])->name('kegiatan');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [AdminController::class, 'index'])->name('dashboard');
    Route::get('data-guru', function() {
        return Inertia::render('data-guru');
    })->name('data-guru');

    // Berita
    Route::get('/news', [BeritaController::class, 'berita']);
    Route::post('/add/berita', [BeritaController::class, 'addBerita']);
    Route::delete('/delete/berita/{id}', [BeritaController::class, 'destroy']);
    Route::get('/detail/berita', [BeritaController::class, 'detail']);

    // Prestasi
    Route::get('/trophy', [PrestasiController::class, 'index']);
    Route::post('/prestasi/add', [PrestasiController::class, 'store']);
    Route::delete('/prestasi/{id}', [PrestasiController::class, 'destroy']);

    // Setting
    Route::get('/headerData', [SettingController::class, 'header']);
    Route::get('/setting', [SettingController::class, 'index']);
    Route::post('/add/header', [SettingController::class, 'addHeader']);
    Route::get('/header/{id}/delete', [SettingController::class, 'deleteHeader']);
    Route::get('/manage-user', [SettingController::class, 'manageUser']);
    Route::post('/users', [SettingController::class, 'addUser']);
    Route::get('/users/{id}/delete', [SettingController::class, 'destroy']);
    // Galeri/Kegiatan
    Route::get('/galeri', [AdminController::class, 'kegiatan']);
    Route::post('/add/kegiatan', [AdminController::class, 'addKegiatan']);
    Route::put('/update/kegiatan/{id}', [AdminController::class, 'updateKegiatan']);
    Route::delete('/delete/kegiatan/{id}', [AdminController::class, 'deleteKegiatan']);
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
