import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { router, usePage } from '@inertiajs/react';
import {
    ArrowLeft,
    Calendar,
    Users,
    GraduationCap,
    Heart,
    Eye,
    Share2,
    Filter,
    Grid3X3,
    List,
    Search,
    X,
    ChevronLeft,
    ChevronRight,
    Download,
    ZoomIn
} from 'lucide-react';

interface KegiatanItem {
    id: number;
    judul: string;
    deskripsi: string;
    tanggal: string;
    gambar: string[];
    kategori: 'siswa' | 'guru' | 'karya';
    author: string;
    likes: number;
    views: number;
    tags: string[];
}

interface PageKegiatanProps {
    initialData?: KegiatanItem[];
}

export default function PageKegiatan({ initialData = [] }: PageKegiatanProps) {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const filterParam = urlParams.get('filter') as 'siswa' | 'guru' | 'karya' | null;

    const [kegiatanData, setKegiatanData] = useState<KegiatanItem[]>(initialData);
    const [filteredData, setFilteredData] = useState<KegiatanItem[]>(initialData);
    const [activeFilter, setActiveFilter] = useState<'all' | 'siswa' | 'guru' | 'karya'>(filterParam || 'all');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedImage, setSelectedImage] = useState<{ src: string; title: string } | null>(null);
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [loading, setLoading] = useState(true);

    // Mock data for demonstration
    const mockData: KegiatanItem[] = [
        {
            id: 1,
            judul: "Lomba Sains Nasional 2024",
            deskripsi: "Siswa-siswi MTs Negeri 4 Gunungkidul berhasil meraih prestasi gemilang dalam Lomba Sains Nasional tingkat provinsi dengan meraih juara 1 bidang Matematika dan juara 2 bidang IPA.",
            tanggal: "2024-03-15",
            gambar: [
                "/assets/kegiatan/lomba-sains-1.jpg",
                "/assets/kegiatan/lomba-sains-2.jpg",
                "/assets/kegiatan/lomba-sains-3.jpg"
            ],
            kategori: 'siswa',
            author: "Tim Redaksi",
            likes: 45,
            views: 234,
            tags: ["lomba", "sains", "prestasi", "matematika"]
        },
        {
            id: 2,
            judul: "Workshop Pembelajaran Digital",
            deskripsi: "Para guru mengikuti workshop pembelajaran digital untuk meningkatkan kualitas pengajaran di era modern. Workshop ini membahas penggunaan teknologi dalam pembelajaran.",
            tanggal: "2024-03-10",
            gambar: [
                "/assets/kegiatan/workshop-1.jpg",
                "/assets/kegiatan/workshop-2.jpg"
            ],
            kategori: 'guru',
            author: "Humas Sekolah",
            likes: 32,
            views: 156,
            tags: ["workshop", "digital", "pembelajaran", "teknologi"]
        },
        {
            id: 3,
            judul: "Pameran Karya Seni Siswa",
            deskripsi: "Pameran karya seni siswa menampilkan berbagai hasil kreativitas siswa dalam bidang seni rupa, kerajinan tangan, dan karya tulis.",
            tanggal: "2024-03-08",
            gambar: [
                "/assets/kegiatan/pameran-1.jpg",
                "/assets/kegiatan/pameran-2.jpg",
                "/assets/kegiatan/pameran-3.jpg",
                "/assets/kegiatan/pameran-4.jpg"
            ],
            kategori: 'karya',
            author: "Guru Seni",
            likes: 67,
            views: 289,
            tags: ["seni", "karya", "kreativitas", "pameran"]
        },
        {
            id: 4,
            judul: "Kegiatan Ekstrakurikuler Pramuka",
            deskripsi: "Kegiatan rutin ekstrakurikuler pramuka dengan berbagai aktivitas outdoor dan pembentukan karakter siswa melalui kegiatan kepramukaan.",
            tanggal: "2024-03-05",
            gambar: [
                "/assets/kegiatan/pramuka-1.jpg",
                "/assets/kegiatan/pramuka-2.jpg"
            ],
            kategori: 'siswa',
            author: "Pembina Pramuka",
            likes: 28,
            views: 178,
            tags: ["pramuka", "ekstrakurikuler", "outdoor", "karakter"]
        },
        {
            id: 5,
            judul: "Rapat Koordinasi Guru",
            deskripsi: "Rapat koordinasi bulanan para guru untuk membahas program pembelajaran, evaluasi siswa, dan rencana kegiatan sekolah ke depan.",
            tanggal: "2024-03-01",
            gambar: [
                "/assets/kegiatan/rapat-1.jpg"
            ],
            kategori: 'guru',
            author: "Kepala Sekolah",
            likes: 15,
            views: 89,
            tags: ["rapat", "koordinasi", "pembelajaran", "evaluasi"]
        },
        {
            id: 6,
            judul: "Karya Tulis Ilmiah Siswa",
            deskripsi: "Kumpulan karya tulis ilmiah siswa yang membahas berbagai topik menarik dari lingkungan, teknologi, hingga sosial budaya.",
            tanggal: "2024-02-28",
            gambar: [
                "/assets/kegiatan/karya-tulis-1.jpg",
                "/assets/kegiatan/karya-tulis-2.jpg",
                "/assets/kegiatan/karya-tulis-3.jpg"
            ],
            kategori: 'karya',
            author: "Guru Bahasa Indonesia",
            likes: 41,
            views: 203,
            tags: ["karya tulis", "ilmiah", "penelitian", "siswa"]
        }
    ];

    useEffect(() => {
        // Simulate loading
        const timer = setTimeout(() => {
            setKegiatanData(mockData);
            setFilteredData(mockData);
            setLoading(false);
        }, 1000);

        return () => clearTimeout(timer);
    }, []);

    // Filter and search functionality
    useEffect(() => {
        let filtered = kegiatanData;

        // Apply category filter
        if (activeFilter !== 'all') {
            filtered = filtered.filter(item => item.kategori === activeFilter);
        }

        // Apply search filter
        if (searchQuery) {
            filtered = filtered.filter(item =>
                item.judul.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.deskripsi.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
            );
        }

        setFilteredData(filtered);
    }, [activeFilter, searchQuery, kegiatanData]);

    const handleGoBack = () => {
        router.visit('/');
    };

    const handleImageClick = (src: string, title: string, imageIndex: number = 0) => {
        setSelectedImage({ src, title });
        setCurrentImageIndex(imageIndex);
    };

    const handleNextImage = () => {
        if (selectedImage) {
            const currentItem = filteredData.find(item =>
                item.gambar.some(img => img === selectedImage.src)
            );
            if (currentItem && currentImageIndex < currentItem.gambar.length - 1) {
                setCurrentImageIndex(currentImageIndex + 1);
                setSelectedImage({
                    src: currentItem.gambar[currentImageIndex + 1],
                    title: currentItem.judul
                });
            }
        }
    };

    const handlePrevImage = () => {
        if (selectedImage) {
            const currentItem = filteredData.find(item =>
                item.gambar.some(img => img === selectedImage.src)
            );
            if (currentItem && currentImageIndex > 0) {
                setCurrentImageIndex(currentImageIndex - 1);
                setSelectedImage({
                    src: currentItem.gambar[currentImageIndex - 1],
                    title: currentItem.judul
                });
            }
        }
    };

    const getCategoryIcon = (kategori: string) => {
        switch (kategori) {
            case 'siswa':
                return <GraduationCap className="w-5 h-5" />;
            case 'guru':
                return <Users className="w-5 h-5" />;
            case 'karya':
                return <Heart className="w-5 h-5" />;
            default:
                return <Calendar className="w-5 h-5" />;
        }
    };

    const getCategoryLabel = (kategori: string) => {
        switch (kategori) {
            case 'siswa':
                return 'Kegiatan Siswa';
            case 'guru':
                return 'Kegiatan Guru';
            case 'karya':
                return 'Karya Siswa';
            default:
                return 'Semua';
        }
    };

    const getCategoryColor = (kategori: string) => {
        switch (kategori) {
            case 'siswa':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'guru':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'karya':
                return 'bg-purple-100 text-purple-800 border-purple-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Memuat galeri kegiatan...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
            {/* Header */}
            <div className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <button
                                onClick={handleGoBack}
                                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                            >
                                <ArrowLeft className="w-4 h-4" />
                                Kembali
                            </button>
                            <div>
                                <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
                                    Galeri Kegiatan
                                </h1>
                                <p className="text-gray-600 mt-1">
                                    Dokumentasi kegiatan siswa, guru, dan karya siswa
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Filters and Search */}
            <div className="max-w-7xl mx-auto px-4 py-6">
                <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
                    <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
                        {/* Search */}
                        <div className="relative flex-1 max-w-md">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                            <input
                                type="text"
                                placeholder="Cari kegiatan..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            />
                        </div>

                        {/* Category Filters */}
                        <div className="flex gap-2 flex-wrap">
                            {[
                                { key: 'all', label: 'Semua', icon: Filter },
                                { key: 'siswa', label: 'Kegiatan Siswa', icon: GraduationCap },
                                { key: 'guru', label: 'Kegiatan Guru', icon: Users },
                                { key: 'karya', label: 'Karya Siswa', icon: Heart }
                            ].map(({ key, label, icon: Icon }) => (
                                <button
                                    key={key}
                                    onClick={() => setActiveFilter(key as any)}
                                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all ${
                                        activeFilter === key
                                            ? 'bg-green-600 text-white border-green-600'
                                            : 'bg-white text-gray-700 border-gray-300 hover:border-green-300 hover:text-green-600'
                                    }`}
                                >
                                    <Icon className="w-4 h-4" />
                                    <span className="text-sm font-medium">{label}</span>
                                </button>
                            ))}
                        </div>

                        {/* View Mode Toggle */}
                        <div className="flex bg-gray-100 rounded-lg p-1">
                            <button
                                onClick={() => setViewMode('grid')}
                                className={`p-2 rounded-md transition-colors ${
                                    viewMode === 'grid'
                                        ? 'bg-white text-green-600 shadow-sm'
                                        : 'text-gray-500 hover:text-gray-700'
                                }`}
                            >
                                <Grid3X3 className="w-5 h-5" />
                            </button>
                            <button
                                onClick={() => setViewMode('list')}
                                className={`p-2 rounded-md transition-colors ${
                                    viewMode === 'list'
                                        ? 'bg-white text-green-600 shadow-sm'
                                        : 'text-gray-500 hover:text-gray-700'
                                }`}
                            >
                                <List className="w-5 h-5" />
                            </button>
                        </div>
                    </div>
                </div>

                {/* Results Count */}
                <div className="mb-6">
                    <p className="text-gray-600">
                        Menampilkan {filteredData.length} dari {kegiatanData.length} kegiatan
                        {activeFilter !== 'all' && (
                            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                                {getCategoryLabel(activeFilter)}
                            </span>
                        )}
                    </p>
                </div>

                {/* Gallery Grid */}
                {filteredData.length === 0 ? (
                    <div className="text-center py-16">
                        <div className="text-gray-400 mb-4">
                            <Search className="w-16 h-16 mx-auto" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-600 mb-2">
                            Tidak ada kegiatan ditemukan
                        </h3>
                        <p className="text-gray-500">
                            Coba ubah filter atau kata kunci pencarian
                        </p>
                    </div>
                ) : (
                    <div className={`grid gap-6 ${
                        viewMode === 'grid'
                            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                            : 'grid-cols-1'
                    }`}>
                        {filteredData.map((item, index) => (
                            <motion.div
                                key={item.id}
                                className={`bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group border border-gray-100 hover:border-green-200 ${
                                    viewMode === 'list' ? 'flex gap-6' : ''
                                }`}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                            >
                                {/* Image Gallery */}
                                <div className={`relative ${viewMode === 'list' ? 'w-80 flex-shrink-0' : ''}`}>
                                    <div className="relative aspect-video overflow-hidden">
                                        <img
                                            src={item.gambar[0] || '/assets/placeholder.jpg'}
                                            alt={item.judul}
                                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                        />
                                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />

                                        {/* Image Count Badge */}
                                        {item.gambar.length > 1 && (
                                            <div className="absolute top-3 right-3 bg-black/70 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                +{item.gambar.length - 1} foto
                                            </div>
                                        )}

                                        {/* Zoom Icon */}
                                        <button
                                            onClick={() => handleImageClick(item.gambar[0], item.judul, 0)}
                                            className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                        >
                                            <div className="bg-white/90 p-3 rounded-full">
                                                <ZoomIn className="w-6 h-6 text-gray-700" />
                                            </div>
                                        </button>
                                    </div>

                                    {/* Additional Images Preview */}
                                    {item.gambar.length > 1 && viewMode === 'grid' && (
                                        <div className="absolute bottom-3 left-3 flex gap-1">
                                            {item.gambar.slice(1, 4).map((img, imgIndex) => (
                                                <button
                                                    key={imgIndex}
                                                    onClick={() => handleImageClick(img, item.judul, imgIndex + 1)}
                                                    className="w-8 h-8 rounded overflow-hidden border-2 border-white shadow-sm hover:scale-110 transition-transform"
                                                >
                                                    <img
                                                        src={img}
                                                        alt=""
                                                        className="w-full h-full object-cover"
                                                    />
                                                </button>
                                            ))}
                                            {item.gambar.length > 4 && (
                                                <div className="w-8 h-8 rounded bg-black/70 text-white text-xs flex items-center justify-center border-2 border-white">
                                                    +{item.gambar.length - 4}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>

                                {/* Content */}
                                <div className="p-6 flex-1">
                                    {/* Category Badge */}
                                    <div className="flex items-center gap-2 mb-3">
                                        <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium border ${getCategoryColor(item.kategori)}`}>
                                            {getCategoryIcon(item.kategori)}
                                            {getCategoryLabel(item.kategori)}
                                        </span>
                                        <span className="text-xs text-gray-500">
                                            {new Date(item.tanggal).toLocaleDateString('id-ID', {
                                                day: 'numeric',
                                                month: 'long',
                                                year: 'numeric'
                                            })}
                                        </span>
                                    </div>

                                    {/* Title */}
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                        {item.judul}
                                    </h3>

                                    {/* Description */}
                                    <p className={`text-gray-600 mb-4 ${viewMode === 'grid' ? 'line-clamp-3' : 'line-clamp-2'}`}>
                                        {item.deskripsi}
                                    </p>

                                    {/* Tags */}
                                    <div className="flex flex-wrap gap-1 mb-4">
                                        {item.tags.slice(0, 3).map((tag, tagIndex) => (
                                            <span
                                                key={tagIndex}
                                                className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                                            >
                                                #{tag}
                                            </span>
                                        ))}
                                        {item.tags.length > 3 && (
                                            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                                                +{item.tags.length - 3}
                                            </span>
                                        )}
                                    </div>

                                    {/* Stats and Actions */}
                                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                                        <div className="flex items-center gap-4 text-sm text-gray-500">
                                            <div className="flex items-center gap-1">
                                                <Heart className="w-4 h-4" />
                                                <span>{item.likes}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Eye className="w-4 h-4" />
                                                <span>{item.views}</span>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                                                <Share2 className="w-4 h-4" />
                                            </button>
                                            <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                                                <Download className="w-4 h-4" />
                                            </button>
                                        </div>
                                    </div>

                                    {/* Author */}
                                    <div className="mt-3 pt-3 border-t border-gray-100">
                                        <p className="text-xs text-gray-500">
                                            Oleh: <span className="font-medium">{item.author}</span>
                                        </p>
                                    </div>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                )}
            </div>

            {/* Image Modal */}
            <AnimatePresence>
                {selectedImage && (
                    <motion.div
                        className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={() => setSelectedImage(null)}
                    >
                        <motion.div
                            className="relative max-w-4xl max-h-full"
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.8, opacity: 0 }}
                            onClick={(e) => e.stopPropagation()}
                        >
                            {/* Close Button */}
                            <button
                                onClick={() => setSelectedImage(null)}
                                className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors"
                            >
                                <X className="w-8 h-8" />
                            </button>

                            {/* Navigation Buttons */}
                            {(() => {
                                const currentItem = filteredData.find(item =>
                                    item.gambar.some(img => img === selectedImage.src)
                                );
                                return currentItem && currentItem.gambar.length > 1 && (
                                    <>
                                        {currentImageIndex > 0 && (
                                            <button
                                                onClick={handlePrevImage}
                                                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-colors"
                                            >
                                                <ChevronLeft className="w-6 h-6" />
                                            </button>
                                        )}
                                        {currentImageIndex < currentItem.gambar.length - 1 && (
                                            <button
                                                onClick={handleNextImage}
                                                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-colors"
                                            >
                                                <ChevronRight className="w-6 h-6" />
                                            </button>
                                        )}
                                    </>
                                );
                            })()}

                            {/* Image */}
                            <img
                                src={selectedImage.src}
                                alt={selectedImage.title}
                                className="max-w-full max-h-full object-contain rounded-lg"
                            />

                            {/* Image Info */}
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 rounded-b-lg">
                                <h3 className="text-white text-lg font-semibold mb-2">
                                    {selectedImage.title}
                                </h3>
                                {(() => {
                                    const currentItem = filteredData.find(item =>
                                        item.gambar.some(img => img === selectedImage.src)
                                    );
                                    return currentItem && currentItem.gambar.length > 1 && (
                                        <p className="text-white/80 text-sm">
                                            {currentImageIndex + 1} dari {currentItem.gambar.length} foto
                                        </p>
                                    );
                                })()}
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
}